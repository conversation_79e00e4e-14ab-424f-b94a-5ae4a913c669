import { useState } from 'react';
import { useGetVideos } from '../hooks/useGetVideos';
import type { Video, PaginatedResponse } from '../api/videoApi';

// Icons
const SortIcon = () => (
  <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4" />
  </svg>
);

const ChevronLeftIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
  </svg>
);

const ChevronRightIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
  </svg>
);

const VideoList = () => {
  const [searchText, setSearchText] = useState('');
  const [pageNumber, setPageNumber] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [sortBy, setSortBy] = useState('createdAt');
  const [sortDescending, setSortDescending] = useState(true);

  // Combine individual states for the API call
  const searchParams = {
    searchText,
    pageNumber,
    pageSize,
    sortBy,
    sortDescending,
  };

  const {
    data,
    isLoading,
    isError,
    isPreviousData
  } = useGetVideos(searchParams);
  
  // Type guard to check if data is defined and has the expected structure
  const hasData = (data: any): data is PaginatedResponse<Video> => {
    return data && Array.isArray(data.items);
  };

  const handleSearch = (e: React.FormEvent) => {
    console.log('handleSearch   ------');
    e.preventDefault();
    setPageNumber(1); // Reset to first page on new search
  };

  const handlePageChange = (newPage: number) => {
    if (!isPreviousData && data?.hasPreviousPage) {
      setPageNumber(newPage);
    }
  };

  const onChangeSearchText = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.value === searchText) return;

    console.log('onChangeSearchText', e.target.value);
    setSearchText(e.target.value);
  };

  if (isLoading) {
    return <div>Loading videos...</div>;
  }

  if (isError) {
    return <div>Error loading videos. Please try again later.</div>;
  }

  return (
    <div className="max-w-7xl mx-auto p-5">
      <h1 className="text-2xl font-bold mb-6 text-gray-800 dark:text-white">Video Search</h1>
      
      <form onSubmit={handleSearch} className="flex gap-2.5 mb-5">
        <input
          type="text"
          className="flex-1 p-2.5 border border-gray-300 rounded text-white-700 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 focus:outline-none"
          value={searchText}
          onChange={onChangeSearchText}
          placeholder="Search videos..."
        />
        <button 
          type="submit" 
          className="px-4 py-2.5 bg-blue-500 text-white rounded hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        >
          Search
        </button>
      </form>

      <div className="flex flex-wrap items-center justify-between mb-6 gap-4">
        <div className="flex items-center space-x-4">
          <div className="relative">
            <label htmlFor="sort-by" className="block text-sm font-medium text-gray-700 mb-1 dark:text-gray-300">Sort by</label>
            <div className="relative">
              <select
                id="sort-by"
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="appearance-none bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-200 py-2 px-4 pr-8 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="createdAt">Date Created</option>
                <option value="title">Title</option>
              </select>
              <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700 dark:text-gray-300">
                <SortIcon />
              </div>
            </div>
          </div>
          
          <div className="flex items-center mt-6">
            <div className="flex items-center">
              <input
                id="sort-descending"
                type="checkbox"
                checked={sortDescending}
                onChange={(e) => setSortDescending(e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded dark:border-gray-600 dark:bg-gray-700"
              />
              <label htmlFor="sort-descending" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                Descending
              </label>
            </div>
          </div>
        </div>
        
        {hasData(data) && (
          <div className="text-sm text-gray-500 dark:text-gray-400">
            Showing {data.items.length} of {data.totalCount} videos
          </div>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        {hasData(data) && data.items.map((video: Video) => (
          <div key={video.id} className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
            <div className="relative pb-[56.25%] bg-gray-100">
              <img 
                src={video.thumbnailUrl} 
                alt={video.title} 
                className="absolute inset-0 w-full h-full object-cover"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.src = 'https://via.placeholder.com/640x360?text=No+Thumbnail';
                }}
              />
              <div className="absolute bottom-2 right-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded">
                {Math.floor(video.duration / 60)}:{(video.duration % 60).toString().padStart(2, '0')}
              </div>
            </div>
            <div className="p-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
                {video.title}
              </h3>
              <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                {video.description || 'No description available'}
              </p>
              <div className="flex justify-between items-center text-xs text-gray-500">
                <span>Uploaded: {new Date(video.createdAt).toLocaleDateString()}</span>
              </div>
            </div>
          </div>
        ))}
      </div>

      {hasData(data) && data.totalPages > 1 && (
        <div className="flex items-center justify-between border-t border-gray-200 pt-6">
          <div className="flex-1 flex justify-between sm:hidden">
            <button
              onClick={() => handlePageChange(pageNumber - 1)}
              disabled={pageNumber === 1}
              className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>
            <button
              onClick={() => handlePageChange(pageNumber + 1)}
              disabled={isPreviousData || !hasData(data) || !data.hasNextPage}
              className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
            </button>
          </div>
          <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p className="text-sm text-gray-700 dark:text-gray-300">
                Showing <span className="font-medium">{(pageNumber - 1) * pageSize + 1}</span> to{' '}
                <span className="font-medium">
                  {hasData(data) ? Math.min(pageNumber * pageSize, data.totalCount) : 0}
                </span>{' '}
                of <span className="font-medium">{hasData(data) ? data.totalCount : 0}</span> results
              </p>
            </div>
            <div>
              <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                <button
                  onClick={() => handlePageChange(pageNumber - 1)}
                  disabled={pageNumber === 1}
                  className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <span className="sr-only">Previous</span>
                  <ChevronLeftIcon />
                </button>
                {Array.from({ length: Math.min(5, data.totalPages) }, (_, i) => {
                  let pageNum;
                  if (data.totalPages <= 5) {
                    pageNum = i + 1;
                  } else if (pageNumber <= 3) {
                    pageNum = i + 1;
                  } else if (pageNumber >= data.totalPages - 2) {
                    pageNum = data.totalPages - 4 + i;
                  } else {
                    pageNum = pageNumber - 2 + i;
                  }

                  return (
                    <button
                      key={pageNum}
                      onClick={() => handlePageChange(pageNum)}
                      className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                        pageNumber === pageNum
                          ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                          : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                      }`}
                    >
                      {pageNum}
                    </button>
                  );
                })}
                <button
                  onClick={() => handlePageChange(pageNumber + 1)}
                  disabled={isPreviousData || !data?.hasNextPage}
                  className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <span className="sr-only">Next</span>
                  <ChevronRightIcon />
                </button>
              </nav>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default VideoList;
